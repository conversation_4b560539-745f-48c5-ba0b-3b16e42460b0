substitutions:
    uv_ao_pin: GPIO35
    uv_update_interval: '5s'
    uv_voltage_offset: '140' # mV

globals:
    - id: uv_voltage_offset
      type: float
      restore_value: false
      initial_value: $uv_voltage_offset

sensor:
    - platform: adc
      id: uv_sensor_voltage
      name: 'UV Sensor Voltage'
      icon: 'mdi:weather-sunny-alert'
      unit_of_measurement: 'mV'
      device_class: voltage
      accuracy_decimals: 0
      entity_category: diagnostic
      update_interval: $uv_update_interval
      pin: $uv_ao_pin
      attenuation: 2.5db
      filters:
          - multiply: 1000.0

    - platform: copy
      source_id: uv_sensor_voltage
      id: uv_index
      name: 'UV Index'
      icon: 'mdi:white-balance-sunny'
      accuracy_decimals: 1
      unit_of_measurement: 'UVI'
      device_class: ''
      lambda: |-
          const float uv_table_mv[] = {0, 227, 318, 408, 503, 606, 696, 795, 881, 976, 1079, 1170};
          const float uv_index[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};

          float mv = (id(uv_sensor_voltage).state * 1000.0) - id(uv_voltage_offset);

          if (mv < 50.0) {
              return 0;
          }

          for (int i = 0; i < 11; i++) {
              if (mv >= uv_table_mv[i] && mv <= uv_table_mv[i + 1]) {
                  float slope = (uv_index[i+1] - uv_index[i]) / (uv_table_mv[i+1] - uv_table_mv[i]);
                  return uv_index[i] + (slope * (mv - uv_table_mv[i]));
              }
          }

          return 11;
